/**
 * WorldInfoOptimizer - A comprehensive TypeScript implementation for SillyTavern world book management
 *
 * This tool provides advanced world book management capabilities including:
 * - Global, character, and chat worldbook management
 * - Batch operations (enable, disable, delete entries)
 * - Advanced search and filtering
 * - Regex management with drag-and-drop sorting
 * - Real-time search highlighting
 * - Global text replacement functionality
 * - Worldbook renaming with automatic binding updates
 *
 * Based on the technical documentation and following SillyTavern extension patterns.
 *
 * @version 1.0.0
 * <AUTHOR> Team
 * @license MIT
 */
