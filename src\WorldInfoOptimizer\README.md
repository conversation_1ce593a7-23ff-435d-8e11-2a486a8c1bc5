# WorldInfoOptimizer - SillyTavern 世界书优化器

## 概述

WorldInfoOptimizer 是一个为 SillyTavern 开发的强大世界书管理工具，提供了全面的世界书管理功能，包括创建、编辑、搜索、排序和批量操作等功能。

## 核心功能

### 1. 世界书数据管理
- **全局世界书管理**: 管理所有全局世界书及其条目
- **角色世界书管理**: 管理与当前角色绑定的世界书
- **聊天世界书管理**: 管理与当前聊天绑定的世界书
- **数据缓存机制**: 智能缓存已加载的数据，提高性能

### 2. 用户界面功能
- **多标签页界面**: 全局、角色、聊天、正则四个主要视图
- **响应式设计**: 适配不同屏幕尺寸
- **直观的操作界面**: 用户友好的按钮和控件布局

### 3. 批量操作功能
- **多选模式**: 支持批量选择世界书条目
- **批量启用/禁用**: 一键启用或禁用多个条目
- **批量删除**: 安全删除多个选中的条目
- **全选/全不选**: 快速选择操作

### 4. 搜索与过滤功能
- **多维度搜索**: 支持按书名、条目名、关键词、内容搜索
- **实时搜索**: 输入时即时显示搜索结果
- **搜索过滤器**: 可自定义搜索范围
- **搜索高亮**: 匹配的文本会被高亮显示

### 5. 正则表达式管理
- **全局和角色正则**: 分别管理不同作用域的正则表达式
- **拖拽排序**: 支持拖拽调整正则表达式执行顺序
- **状态管理**: 启用/禁用正则表达式
- **批量操作**: 支持正则表达式的批量管理

### 6. 高级功能
- **世界书重命名**: 重命名世界书并自动更新所有相关绑定
- **绑定管理**: 管理角色和聊天的世界书绑定关系
- **错误处理**: 完善的错误处理和用户反馈机制
- **性能优化**: 分批加载、防抖处理等性能优化

## 技术特性

### 架构设计
- **TypeScript 实现**: 完整的类型安全和智能提示
- **模块化设计**: 清晰的代码结构和职责分离
- **事件驱动**: 基于事件的用户交互处理
- **状态管理**: 集中的应用状态管理

### 性能优化
- **数据缓存**: 使用 Map 结构缓存世界书数据
- **分批加载**: 避免同时加载过多数据
- **防抖处理**: 优化频繁的用户输入操作
- **异步处理**: 使用 Promise.allSettled 确保稳定性

### 错误处理
- **多层错误处理**: API层、数据层、UI层的完整错误处理
- **用户友好提示**: 清晰的错误信息和操作反馈
- **降级处理**: 单个功能失败不影响整体使用

## 使用方法

### 基本使用
1. 在 SillyTavern 扩展菜单中找到"世界书＆正则便捷管理"按钮
2. 点击按钮打开管理界面
3. 在不同标签页之间切换查看不同类型的世界书
4. 使用搜索功能快速定位所需内容
5. 使用批量操作功能高效管理多个条目

### 高级功能
- **多选模式**: 点击"多选模式"按钮进入批量操作模式
- **搜索过滤**: 使用搜索框和过滤器精确查找内容
- **拖拽排序**: 在正则表达式视图中拖拽调整顺序
- **绑定管理**: 在角色和聊天视图中管理世界书绑定

## API 集成

本工具完全基于 SillyTavern 的 TavernHelper API，包括：
- 世界书操作 API
- 正则表达式管理 API
- 角色和聊天绑定 API
- 设置管理 API

## 兼容性

- **SillyTavern**: 兼容最新版本的 SillyTavern
- **浏览器**: 支持现代浏览器（Chrome, Firefox, Safari, Edge）
- **设备**: 支持桌面和移动设备

## 开发信息

- **语言**: TypeScript
- **框架**: 原生 DOM 操作
- **样式**: CSS3 + 响应式设计
- **构建**: 支持 ES6+ 和 CommonJS 导出

## 许可证

MIT License - 详见 LICENSE 文件

## 贡献

欢迎提交 Issue 和 Pull Request 来改进这个工具。

## 更新日志

### v1.0.0
- 初始版本发布
- 实现所有核心功能
- 完整的 TypeScript 类型定义
- 响应式用户界面
- 完善的错误处理机制
