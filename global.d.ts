declare module '*?raw' {
  const content: string;
  export default content;
}
declare module '*.html' {
  const content: string;
  export default content;
}
declare module '*.css' {
  const content: unknown;
  export default content;
}
declare module '*.vue' {
  import { DefineComponent } from 'vue';
  const component: DefineComponent<{}, {}, any>;
  export default component;
}

declare const YAML: typeof import('yaml');

declare const z: typeof import('zod');
declare namespace z {
  export type infer<T> = import('zod').infer<T>;
  export type input<T> = import('zod').input<T>;
  export type output<T> = import('zod').output<T>;
}

// Global declarations for SillyTavern
declare const SillyTavern: any;
declare const TavernHelper: any;
declare const $: typeof import('jquery');
declare const toastr: any;
declare const document: Document;
declare const window: Window & {
  WorldInfoOptimizer?: typeof WorldInfoOptimizer;
};

// Type for partial deep objects (used in world book API)
type PartialDeep<T> = T extends object
  ? {
      [P in keyof T]?: PartialDeep<T[P]>;
    }
  : T;

// Node.js types for module exports
declare const module: { exports: any };
